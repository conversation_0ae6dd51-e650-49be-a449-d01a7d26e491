---
alwaysApply: true
---
FontAwesome Icon Management
**Requirement**: Centralized icon management to use local downloaded  icons.  Use single service to prevent massive errors.
**Implementation**:
- [ ] Single icon registry file: `src/components/Icons/index.ts`
- [ ] All icons imported from registry only
- [ ] No direct FontAwesome imports in components, ensure no embedded/CDN from the internet
- [ ] Downloaded icons and must be offline accessible
- [ ] **Icon Storage Location**: All downloaded icons must be placed in `\src\components\Icons` folder
- [ ] **Offline Accessibility**: All icons must be offline accessible through the local folder `\src\components\Icons`
- [ ] **Development Icons Source**: Discover development icons from `\src\components\Icons\fontawesome-free-7.0.0-web` folder or `\src\components\Icons\fontawesome-free-7.0.0-desktop`
- [ ] **Only Icons in Use**: Only icons that are actively used in the application should be stored locally, through the local folder `\src\components\Icons`  I will check out when packaging.
**Enforcement**:
Use ESlint to block all connections to FontAwesome CDN.
Remove ESlint when deployment.


### Rule 2.3: Interface Consistency
**Requirement**: Prevent duplicate type definitions
**Implementation**:
- [ ] Single source of truth for all interfaces
- [ ] Global types in `src/types/index.ts`
- [ ] Preload types must match global types
- [ ] Regular interface synchronization checks
**Enforcement**:
- [ ] TypeScript strict mode enabled
- [ ] Automated type consistency validation
- [ ] Pre-commit TypeScript check