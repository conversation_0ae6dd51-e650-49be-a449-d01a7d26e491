/**
 * Intelligence System Integration Test
 * Tests the document intelligence collection system
 */

import { documentIntelligenceService } from '../services/documentIntelligenceService'
import { DocumentMetadata, SmartAnnotationConfig } from '../types/intelligenceTypes'

// Test configuration
const testConfig: SmartAnnotationConfig = {
  ai_model: 'gemma3-32k',
  confidence_threshold: 0.6,
  max_entities: 10,
  enable_relationships: true,
  auto_save: false, // Don't save during testing
  include_content_analysis: true
}

// Sample test document content
const testDocumentContent = `
# Project Specification Document

## Overview
This document outlines the requirements for building a React-based user interface component library. The library should include reusable components, design tokens, and comprehensive documentation.

## Technical Requirements
- React 18+ compatibility
- TypeScript support
- Tailwind CSS integration
- Storybook documentation
- Jest testing framework
- ESLint and Prettier configuration

## Components to Implement
1. Button component with variants
2. Input field with validation
3. Modal dialog system
4. Navigation components
5. Data table with sorting
6. Form components with validation

## Design System
The design system should follow modern UI/UX principles with:
- Consistent spacing and typography
- Accessible color palette
- Responsive design patterns
- Dark mode support

## Testing Strategy
All components must have:
- Unit tests with Jest
- Integration tests
- Visual regression tests
- Accessibility tests

## Deployment
The library will be published to npm and should support:
- Tree shaking
- Bundle size optimization
- TypeScript declarations
- Comprehensive documentation
`

// Test function to verify intelligence extraction
export async function testIntelligenceExtraction(): Promise<void> {
  console.log('🧠 Testing Intelligence System Integration...')
  
  try {
    // Test 1: Document Analysis
    console.log('\n📄 Test 1: Document Analysis')
    const analysis = await documentIntelligenceService.analyzeDocument(
      '/test/project-spec.md',
      testDocumentContent,
      testConfig
    )
    
    console.log('✅ Analysis completed:', {
      entitiesFound: analysis.extracted_entities.length,
      insightsGenerated: analysis.key_insights.length,
      confidenceScore: Math.round(analysis.confidence_score * 100) + '%',
      processingTime: analysis.processing_time_ms + 'ms'
    })
    
    // Test 2: Entity Extraction Verification
    console.log('\n🏷️  Test 2: Entity Extraction')
    const entities = analysis.extracted_entities
    console.log('Extracted entities:')
    entities.forEach((entity, index) => {
      console.log(`  ${index + 1}. ${entity.entity} (${entity.type}) - ${Math.round(entity.confidence * 100)}%`)
    })
    
    // Test 3: Insights Generation
    console.log('\n💡 Test 3: Key Insights')
    const insights = analysis.key_insights
    console.log('Generated insights:')
    insights.forEach((insight, index) => {
      console.log(`  ${index + 1}. [${insight.importance.toUpperCase()}] ${insight.insight}`)
    })
    
    // Test 4: Session Creation
    console.log('\n📝 Test 4: Session Creation')
    const documentMetadata: DocumentMetadata = {
      path: '/test/project-spec.md',
      name: 'project-spec.md',
      type: 'markdown',
      size: testDocumentContent.length,
      hash: 'test-hash-' + Date.now(),
      vault: 'test-vault',
      last_modified: new Date().toISOString()
    }
    
    const session = documentIntelligenceService.createSession(
      documentMetadata,
      analysis
    )
    
    console.log('✅ Session created:', {
      sessionId: session.session_id,
      documentHash: session.document.hash,
      entitiesCount: session.intelligence_session.extracted_entities.length,
      userInteractions: session.user_interactions.length
    })
    
    // Test 5: Data Export
    console.log('\n📤 Test 5: Data Export')
    const exportData = await documentIntelligenceService.exportIntelligenceData(
      documentMetadata.hash
    )
    
    console.log('✅ Export completed:', {
      documentHash: exportData.documentHash,
      exportedAt: exportData.exportedAt
    })
    
    console.log('\n🎉 All tests completed successfully!')
    
    return Promise.resolve()
    
  } catch (error) {
    console.error('❌ Intelligence system test failed:', error)
    throw error
  }
}

// Test function for UI component integration
export function testUIComponentIntegration(): void {
  console.log('\n🎨 Testing UI Component Integration...')
  
  // Test SmartAnnotationPanel props
  const testProps = {
    selectedFile: 'project-spec.md',
    filePath: '/test/project-spec.md',
    fileContent: testDocumentContent,
    selectedContextId: 'test-context',
    onAnnotationComplete: (analysis: any) => {
      console.log('✅ Annotation completed callback triggered:', {
        entitiesCount: analysis.extracted_entities?.length || 0,
        confidence: Math.round((analysis.confidence_score || 0) * 100) + '%'
      })
    },
    onEntitySelect: (entities: any[]) => {
      console.log('✅ Entity selection callback triggered:', {
        selectedCount: entities.length,
        entities: entities.map(e => e.entity).join(', ')
      })
    }
  }
  
  console.log('✅ UI component props validated:', {
    hasRequiredProps: !!(testProps.selectedFile && testProps.fileContent),
    callbacksConfigured: typeof testProps.onAnnotationComplete === 'function' && typeof testProps.onEntitySelect === 'function'
  })
}

// Integration test for FileActionsPanel
export function testFileActionsPanelIntegration(): void {
  console.log('\n⚙️  Testing FileActionsPanel Integration...')
  
  // Verify Smart Annotation action is available
  const smartAnnotationAction = {
    id: 'smart-annotation',
    label: 'Smart Annotation',
    icon: 'magic', // Should map to faWandMagic
    description: 'AI-powered entity extraction and insights',
    disabled: false
  }
  
  console.log('✅ Smart Annotation action configured:', smartAnnotationAction)
  
  // Test action handler
  const mockActionHandler = (action: string, data?: any) => {
    console.log('✅ Action handler called:', { action, data })
  }
  
  // Simulate action trigger
  mockActionHandler('smart-annotation', { 
    file: 'test.md', 
    path: '/test/test.md' 
  })
}

// Main test runner
export async function runIntelligenceSystemTests(): Promise<void> {
  console.log('🚀 Starting Intelligence System Tests...')
  console.log('=' .repeat(50))
  
  try {
    // Run all tests
    await testIntelligenceExtraction()
    testUIComponentIntegration()
    testFileActionsPanelIntegration()
    
    console.log('\n' + '=' .repeat(50))
    console.log('🎉 All Intelligence System Tests Passed!')
    
  } catch (error) {
    console.log('\n' + '=' .repeat(50))
    console.error('❌ Intelligence System Tests Failed:', error)
    throw error
  }
}

// Export for use in development
if (typeof window !== 'undefined') {
  (window as any).testIntelligenceSystem = runIntelligenceSystemTests
  console.log('💡 Intelligence system tests available at: window.testIntelligenceSystem()')
}
