import React, { useState, useEffect, useRef } from 'react'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { ICONS } from './Icons/index'
import { SmartAnnotation, TextPosition } from '../types/intelligenceTypes'

interface SmartAnnotationSystemProps {
  annotations: SmartAnnotation[]
  selectedText?: string
  selectedTextPosition?: TextPosition
  onAnnotationCreate: (content: string, selectedText?: string, position?: TextPosition) => void
  onAnnotationUpdate: (annotationId: string, content: string) => void
  onAnnotationDelete: (annotationId: string) => void
  onSmartAnalyze: () => void
  isAnalyzing?: boolean
}

export const SmartAnnotationSystem: React.FC<SmartAnnotationSystemProps> = ({
  annotations,
  selectedText,
  selectedTextPosition,
  onAnnotationCreate,
  onAnnotationUpdate,
  onAnnotationDelete,
  onSmartAnalyze,
  isAnalyzing = false
}) => {
  const [editingId, setEditingId] = useState<string | null>(null)
  const [editContent, setEditContent] = useState('')
  const [newAnnotation, setNewAnnotation] = useState('')
  const [showNewAnnotation, setShowNewAnnotation] = useState(false)
  const textareaRef = useRef<HTMLTextAreaElement>(null)

  useEffect(() => {
    if (selectedText && !showNewAnnotation) {
      setShowNewAnnotation(true)
      setNewAnnotation(`Selected: "${selectedText}"\n\nAnnotation: `)
    }
  }, [selectedText])

  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto'
      textareaRef.current.style.height = textareaRef.current.scrollHeight + 'px'
    }
  }, [editContent, newAnnotation])

  const handleCreateAnnotation = () => {
    if (newAnnotation.trim()) {
      onAnnotationCreate(newAnnotation.trim(), selectedText, selectedTextPosition)
      setNewAnnotation('')
      setShowNewAnnotation(false)
    }
  }

  const handleEditStart = (annotation: SmartAnnotation) => {
    setEditingId(annotation.annotation_id)
    setEditContent(annotation.content)
  }

  const handleEditSave = (annotationId: string) => {
    if (editContent.trim()) {
      onAnnotationUpdate(annotationId, editContent.trim())
    }
    setEditingId(null)
    setEditContent('')
  }

  const handleEditCancel = () => {
    setEditingId(null)
    setEditContent('')
  }

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const generateMockAIAnnotation = () => {
    const mockAnnotations = [
      "This document outlines a comprehensive design system with clear component specifications and implementation guidelines. The focus on consistency and reusability suggests a mature approach to UI development.",
      "Key technical requirements include React/TypeScript implementation with Tailwind CSS. The color palette and typography choices indicate a modern, professional design approach.",
      "The document structure follows best practices for design system documentation, with clear sections for components, guidelines, and technical specifications."
    ]
    
    const randomAnnotation = mockAnnotations[Math.floor(Math.random() * mockAnnotations.length)]
    onAnnotationCreate(randomAnnotation, undefined, undefined)
  }

  return (
    <div className="flex-1 overflow-y-auto">
      {/* Smart Annotation Header */}
      <div className="p-3 border-b border-tertiary/50">
        <div className="flex items-center justify-between mb-2">
          <h3 className="text-supplement1 font-semibold text-sm">Smart Annotation</h3>
          <button
            onClick={onSmartAnalyze}
            disabled={isAnalyzing}
            className="bg-primary hover:bg-primary/80 disabled:bg-primary/50 text-gray-900 font-semibold py-1 px-2 rounded text-xs transition-colors flex items-center gap-1"
          >
            <FontAwesomeIcon 
              icon={ICONS.wandMagicSparkles} 
              className={`text-xs ${isAnalyzing ? 'animate-spin' : ''}`} 
            />
            <span>{isAnalyzing ? 'Analyzing...' : 'AI Analyze'}</span>
          </button>
        </div>
        <p className="text-xs text-gray-400">
          AI-powered insights and your personal notes for context building
        </p>
      </div>

      {/* Selected Text Indicator */}
      {selectedText && (
        <div className="p-3 bg-primary/10 border-b border-primary/20">
          <div className="flex items-start gap-2">
            <FontAwesomeIcon icon={ICONS.textSelect} className="text-primary text-sm mt-0.5" />
            <div className="flex-1">
              <p className="text-primary text-xs font-medium mb-1">Selected Text:</p>
              <p className="text-supplement1 text-xs bg-gray-800/50 p-2 rounded border border-gray-700">
                "{selectedText}"
              </p>
            </div>
          </div>
        </div>
      )}

      {/* New Annotation Form */}
      {showNewAnnotation && (
        <div className="p-3 border-b border-tertiary/50 bg-gray-800/50">
          <div className="flex items-center gap-2 mb-2">
            <FontAwesomeIcon icon={ICONS.plus} className="text-supplement2 text-xs" />
            <span className="text-supplement2 text-xs font-medium">New Annotation</span>
          </div>
          <textarea
            ref={textareaRef}
            value={newAnnotation}
            onChange={(e) => setNewAnnotation(e.target.value)}
            placeholder="Add your thoughts about this document or selected text..."
            className="w-full bg-gray-700 text-supplement1 text-xs p-2 rounded border border-gray-600 focus:border-primary focus:outline-none resize-none min-h-[60px]"
            rows={3}
          />
          <div className="flex items-center justify-between mt-2">
            <div className="flex items-center gap-2">
              <button
                onClick={() => {
                  setNewAnnotation(newAnnotation + '\n\n#important ')
                }}
                className="text-xs text-gray-400 hover:text-supplement2 transition-colors"
              >
                #important
              </button>
              <button
                onClick={() => {
                  setNewAnnotation(newAnnotation + '\n\n#todo ')
                }}
                className="text-xs text-gray-400 hover:text-supplement2 transition-colors"
              >
                #todo
              </button>
              <button
                onClick={() => {
                  setNewAnnotation(newAnnotation + '\n\n#question ')
                }}
                className="text-xs text-gray-400 hover:text-supplement2 transition-colors"
              >
                #question
              </button>
            </div>
            <div className="flex items-center gap-2">
              <button
                onClick={() => {
                  setShowNewAnnotation(false)
                  setNewAnnotation('')
                }}
                className="text-xs text-gray-400 hover:text-gray-300 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleCreateAnnotation}
                disabled={!newAnnotation.trim()}
                className="bg-secondary hover:bg-secondary/80 disabled:bg-gray-600 text-gray-900 disabled:text-gray-400 font-medium py-1 px-2 rounded text-xs transition-colors"
              >
                Save
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Annotations List */}
      <div className="p-3 space-y-3">
        {annotations.length === 0 && !showNewAnnotation && (
          <div className="text-center py-8">
            <FontAwesomeIcon icon={ICONS.lightbulb} className="text-gray-500 text-2xl mb-2" />
            <p className="text-gray-400 text-sm mb-2">No annotations yet</p>
            <button
              onClick={() => setShowNewAnnotation(true)}
              className="text-supplement2 hover:text-supplement1 text-xs transition-colors"
            >
              Add your first annotation
            </button>
          </div>
        )}

        {annotations.map((annotation) => (
          <div
            key={annotation.annotation_id}
            className="bg-gray-800/50 rounded border border-gray-700 p-3"
          >
            <div className="flex items-start justify-between mb-2">
              <div className="flex items-center gap-2">
                <FontAwesomeIcon 
                  icon={annotation.is_ai_generated ? ICONS.robot : ICONS.user} 
                  className={`text-xs ${annotation.is_ai_generated ? 'text-primary' : 'text-supplement2'}`} 
                />
                <span className={`text-xs font-medium ${annotation.is_ai_generated ? 'text-primary' : 'text-supplement2'}`}>
                  {annotation.is_ai_generated ? 'AI Analysis' : 'Your Note'}
                </span>
                {annotation.confidence && (
                  <span className="text-xs text-gray-400">
                    ({Math.round(annotation.confidence * 100)}% confidence)
                  </span>
                )}
              </div>
              <div className="flex items-center gap-1">
                <button
                  onClick={() => handleEditStart(annotation)}
                  className="p-1 hover:bg-gray-700 rounded transition-colors"
                >
                  <FontAwesomeIcon icon={ICONS.penToSquare} className="text-supplement2 text-xs" />
                </button>
                <button
                  onClick={() => onAnnotationDelete(annotation.annotation_id)}
                  className="p-1 hover:bg-gray-700 rounded transition-colors"
                >
                  <FontAwesomeIcon icon={ICONS.trash} className="text-red-400 text-xs" />
                </button>
              </div>
            </div>

            {editingId === annotation.annotation_id ? (
              <div>
                <textarea
                  value={editContent}
                  onChange={(e) => setEditContent(e.target.value)}
                  className="w-full bg-gray-700 text-supplement1 text-xs p-2 rounded border border-gray-600 focus:border-primary focus:outline-none resize-none min-h-[60px]"
                  rows={4}
                />
                <div className="flex items-center justify-end gap-2 mt-2">
                  <button
                    onClick={handleEditCancel}
                    className="text-xs text-gray-400 hover:text-gray-300 transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={() => handleEditSave(annotation.annotation_id)}
                    className="bg-secondary hover:bg-secondary/80 text-gray-900 font-medium py-1 px-2 rounded text-xs transition-colors"
                  >
                    Save
                  </button>
                </div>
              </div>
            ) : (
              <div>
                <p className="text-gray-300 text-xs leading-relaxed whitespace-pre-wrap">
                  {annotation.content}
                </p>
                {annotation.selected_text && (
                  <div className="mt-2 p-2 bg-gray-700/50 rounded border border-gray-600">
                    <p className="text-gray-400 text-xs mb-1">Referenced text:</p>
                    <p className="text-gray-300 text-xs italic">"{annotation.selected_text}"</p>
                  </div>
                )}
                <div className="flex items-center justify-between mt-2">
                  <div className="flex items-center gap-2">
                    {annotation.tags.map((tag, index) => (
                      <span
                        key={index}
                        className="px-1.5 py-0.5 bg-supplement2/20 text-supplement2 text-xs rounded border border-supplement2/30"
                      >
                        #{tag}
                      </span>
                    ))}
                  </div>
                  <span className="text-gray-500 text-xs">
                    {formatTimestamp(annotation.timestamp)}
                  </span>
                </div>
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Quick Actions */}
      {!showNewAnnotation && (
        <div className="p-3 border-t border-tertiary/50">
          <div className="flex items-center gap-2">
            <button
              onClick={() => setShowNewAnnotation(true)}
              className="flex-1 bg-gray-700 hover:bg-gray-600 text-supplement1 font-medium py-2 px-3 rounded text-xs transition-colors flex items-center justify-center gap-1"
            >
              <FontAwesomeIcon icon={ICONS.plus} />
              Add Note
            </button>
            <button
              onClick={generateMockAIAnnotation}
              disabled={isAnalyzing}
              className="flex-1 bg-primary/20 hover:bg-primary/30 border border-primary/30 text-primary font-medium py-2 px-3 rounded text-xs transition-colors flex items-center justify-center gap-1"
            >
              <FontAwesomeIcon icon={ICONS.wandMagicSparkles} />
              AI Insight
            </button>
          </div>
        </div>
      )}
    </div>
  )
}
