# Intelligence Collection Storage Schema

## Overview
The Intelligence Collection System integrates seamlessly with ChatLo's existing vault architecture, storing intelligence data within context vaults using a structured file-based approach.

## Storage Architecture

### Directory Structure
```
<vault_root>/
├── <context_name>/
│   ├── master.md                    # Enhanced with intelligence data
│   ├── documents/                   # User documents
│   ├── .vault/                     # Existing vault metadata
│   └── .intelligence/              # NEW: Intelligence data storage
│       ├── index.json              # Master intelligence index
│       ├── entities/               # Entity data
│       │   ├── entity_index.json   # Global entity index
│       │   └── patterns.json       # Entity patterns and relationships
│       ├── documents/              # Per-document intelligence
│       │   ├── <doc_hash>/
│       │   │   ├── profile.json    # Document profile
│       │   │   ├── sessions/       # Analysis sessions
│       │   │   │   ├── session_<timestamp>.json
│       │   │   │   └── session_<timestamp>.json
│       │   │   └── cache/          # Processed content cache
│       │   │       ├── content.txt # Extracted text content
│       │   │       └── metadata.json
│       │   └── <doc_hash>/
│       ├── learning/               # Learning data
│       │   ├── user_preferences.json
│       │   ├── workflow_patterns.json
│       │   └── content_clusters.json
│       └── exports/                # Export data
│           ├── intelligence_export_<timestamp>.json
│           └── master_updates.jsonl
```

## File Specifications

### 1. Intelligence Index (`index.json`)
Master index for all intelligence data in the vault.

```json
{
  "version": "1.0",
  "vault_path": "/path/to/vault",
  "context_id": "context-uuid",
  "created": "2024-01-15T12:00:00Z",
  "last_updated": "2024-01-15T15:30:00Z",
  "statistics": {
    "total_documents": 25,
    "total_sessions": 47,
    "total_entities": 156,
    "active_workflows": 3
  },
  "configuration": {
    "ai_model": "gemma3-32k",
    "confidence_threshold": 0.7,
    "auto_analysis": true,
    "retention_days": 365
  }
}
```

### 2. Entity Index (`entities/entity_index.json`)
Global registry of all discovered entities across documents.

```json
{
  "version": "1.0",
  "last_updated": "2024-01-15T15:30:00Z",
  "entities": {
    "UI Design": {
      "type": "content_category",
      "total_occurrences": 15,
      "documents": ["doc_hash_1", "doc_hash_2"],
      "user_selection_rate": 0.87,
      "average_confidence": 0.92,
      "first_seen": "2024-01-10T10:00:00Z",
      "last_seen": "2024-01-15T14:20:00Z",
      "relationships": [
        {
          "target": "Component Library",
          "type": "related_to",
          "strength": 0.85
        }
      ]
    }
  }
}
```

### 3. Document Profile (`documents/<hash>/profile.json`)
Comprehensive profile for each analyzed document.

```json
{
  "document_hash": "sha256_hash",
  "path": "/vault/documents/project-spec.pdf",
  "name": "project-spec.pdf",
  "type": "pdf",
  "size": 2048576,
  "created": "2024-01-15T12:00:00Z",
  "last_modified": "2024-01-14T16:30:00Z",
  "intelligence_profile": {
    "total_sessions": 5,
    "key_entities": ["UI Design", "Component Library", "Design Tokens"],
    "user_engagement_score": 0.92,
    "content_importance": "high",
    "workflow_connections": [
      {
        "target_document": "design-tokens.json",
        "relationship": "implements",
        "strength": 0.85,
        "evidence": ["shared entities", "user workflow"]
      }
    ],
    "content_summary": "Comprehensive design system specification...",
    "last_analyzed": "2024-01-15T14:20:00Z"
  }
}
```

### 4. Analysis Session (`documents/<hash>/sessions/session_<timestamp>.json`)
Individual intelligence analysis session data.

```json
{
  "session_id": "uuid-v4",
  "timestamp": "2024-01-15T12:00:00Z",
  "session_type": "smart_annotation",
  "ai_model": "gemma3-32k",
  "processing_time_ms": 1250,
  "confidence_score": 0.87,
  "extracted_entities": [
    {
      "entity": "UI Design",
      "type": "content_category",
      "confidence": 0.95,
      "user_selected": true,
      "context": "Document contains extensive UI design specifications",
      "relationships": [
        {
          "target_entity": "Component Library",
          "relationship_type": "related_to",
          "strength": 0.82
        }
      ]
    }
  ],
  "key_insights": [
    {
      "insight": "Document outlines comprehensive design system",
      "importance": "high",
      "category": "summary",
      "confidence": 0.91,
      "supporting_entities": ["UI Design", "Component Library"]
    }
  ],
  "user_interactions": [
    {
      "action": "tag_selection",
      "timestamp": "2024-01-15T12:01:30Z",
      "data": {
        "selected_tags": ["UI Design", "Specifications"],
        "rejected_tags": ["Architecture", "Testing"]
      },
      "session_id": "uuid-v4"
    }
  ],
  "context_signals": {
    "user_intent": "design_system_review",
    "document_importance": "high",
    "workflow_stage": "specification_analysis",
    "time_spent_seconds": 180,
    "interaction_depth": "deep"
  }
}
```

### 5. User Preferences (`learning/user_preferences.json`)
Learned user preferences and behavior patterns.

```json
{
  "version": "1.0",
  "last_updated": "2024-01-15T15:30:00Z",
  "preferences": {
    "preferred_tags": ["UI Design", "Specifications", "Requirements"],
    "annotation_style": "concise",
    "detail_level": "high",
    "ai_confidence_threshold": 0.75,
    "auto_tag_enabled": true
  },
  "behavior_patterns": {
    "average_session_duration": 240,
    "preferred_analysis_depth": "moderate",
    "tag_selection_patterns": {
      "UI Design": 0.92,
      "Specifications": 0.87,
      "Architecture": 0.23
    }
  }
}
```

## Integration with Existing Systems

### Master.md Enhancement
The existing `master.md` file is enhanced with intelligence data:

```markdown
# Project Context

## Intelligence Summary
**Last Updated**: 2024-01-15T15:30:00Z
**Total Documents Analyzed**: 25
**Key Entities Discovered**: UI Design, Component Library, Design Tokens
**User Engagement Score**: 0.92

### Top Insights
- Comprehensive design system with 47 components identified
- Strong workflow connection between specifications and implementation
- High user engagement with design-related documents

### Workflow Connections
- `project-spec.pdf` → `design-tokens.json` (implements, 0.85)
- `component-library.md` → `ui-guidelines.pdf` (references, 0.78)

## Recent Intelligence Activity
- 2024-01-15: Smart annotation on project-spec.pdf (5 entities, high confidence)
- 2024-01-14: Entity extraction from design-tokens.json (12 entities)
```

### Vault Registry Integration
The existing vault registry is enhanced with intelligence metadata:

```json
{
  "vaults": [
    {
      "id": "vault-uuid",
      "name": "Design System Vault",
      "intelligence": {
        "enabled": true,
        "total_sessions": 47,
        "key_entities": ["UI Design", "Component Library"],
        "last_analysis": "2024-01-15T15:30:00Z"
      }
    }
  ]
}
```

## Performance Considerations

### File Size Management
- Session files are compressed using JSON compression
- Old sessions are archived after 90 days
- Entity index is optimized for fast lookups
- Content cache prevents re-processing

### Concurrent Access
- File locking prevents corruption during writes
- Atomic operations for critical updates
- Backup creation before major changes
- Recovery mechanisms for corrupted data

### Privacy & Security
- All data stored locally within vault structure
- No external API calls for sensitive documents
- User controls data retention and deletion
- Export functionality for data portability

This storage schema ensures seamless integration with ChatLo's existing vault architecture while providing robust intelligence collection capabilities.
