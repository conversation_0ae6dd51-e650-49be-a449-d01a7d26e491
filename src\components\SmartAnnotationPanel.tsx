/**
 * Smart Annotation Panel Component
 * Integrates with FileActionsPanel to provide AI-powered document intelligence
 */

import React, { useState } from 'react'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { ICONS } from './Icons/index'
import { documentIntelligenceService } from '../services/documentIntelligenceService'
import { 
  ExtractedEntity, 

  IntelligenceAnalysis,
  DocumentMetadata,
  SmartAnnotationConfig,
  IntelligenceUIState
} from '../types/intelligenceTypes'

interface SmartAnnotationPanelProps {
  selectedFile?: string
  filePath?: string
  fileContent?: string
  selectedContextId?: string
  onAnnotationComplete?: (analysis: IntelligenceAnalysis) => void
  onEntitySelect?: (entities: ExtractedEntity[]) => void
}

export const SmartAnnotationPanel: React.FC<SmartAnnotationPanelProps> = ({
  selectedFile,
  filePath,
  fileContent,
  selectedContextId,
  onAnnotationComplete,
  onEntitySelect
}) => {
  const [uiState, setUIState] = useState<IntelligenceUIState>({
    isAnalyzing: false,
    currentSession: null,
    suggestedTags: [],
    selectedTags: [],
    confidenceScore: 0,
    insights: [],
    sessionHistory: [],
    currentSessionIndex: 0
  })

  const [config] = useState<SmartAnnotationConfig>({
    ai_model: 'gemma3-32k',
    confidence_threshold: 0.7,
    max_entities: 15,
    enable_relationships: true,
    auto_save: true,
    include_content_analysis: true
  })

  const [expandedSections, setExpandedSections] = useState<Set<string>>(
    new Set(['entities', 'insights'])
  )

  // Trigger smart annotation analysis
  const handleSmartAnnotation = async () => {
    if (!fileContent || !filePath || !selectedFile) {
      console.warn('Missing required data for smart annotation')
      return
    }

    setUIState(prev => ({ ...prev, isAnalyzing: true }))

    try {
      console.log('Starting smart annotation for:', selectedFile)
      
      // Perform AI analysis
      const analysis = await documentIntelligenceService.analyzeDocument(
        filePath,
        fileContent,
        config
      )

      // Create document metadata
      const documentMetadata: DocumentMetadata = {
        path: filePath,
        name: selectedFile,
        type: selectedFile.split('.').pop() || 'unknown',
        size: fileContent.length,
        hash: await generateDocumentHash(fileContent, filePath),
        vault: selectedContextId || 'default',
        last_modified: new Date().toISOString()
      }

      // Create and save session
      const session = documentIntelligenceService.createSession(
        documentMetadata,
        analysis
      )

      if (config.auto_save) {
        await documentIntelligenceService.saveSession(session)
      }

      // Update UI state
      setUIState(prev => ({
        ...prev,
        isAnalyzing: false,
        currentSession: session,
        suggestedTags: analysis.extracted_entities,
        selectedTags: [],
        confidenceScore: analysis.confidence_score,
        insights: analysis.key_insights
      }))

      onAnnotationComplete?.(analysis)
      console.log('Smart annotation completed:', analysis)

    } catch (error) {
      console.error('Smart annotation failed:', error)
      setUIState(prev => ({ ...prev, isAnalyzing: false }))
    }
  }

  // Handle entity selection/deselection
  const handleEntityToggle = (entity: ExtractedEntity) => {
    setUIState(prev => {
      const isSelected = prev.selectedTags.some(tag => tag.entity === entity.entity)
      const newSelectedTags = isSelected
        ? prev.selectedTags.filter(tag => tag.entity !== entity.entity)
        : [...prev.selectedTags, { ...entity, user_selected: true }]

      onEntitySelect?.(newSelectedTags)
      return { ...prev, selectedTags: newSelectedTags }
    })
  }

  // Toggle section expansion
  const toggleSection = (sectionId: string) => {
    setExpandedSections(prev => {
      const newExpanded = new Set(prev)
      if (newExpanded.has(sectionId)) {
        newExpanded.delete(sectionId)
      } else {
        newExpanded.add(sectionId)
      }
      return newExpanded
    })
  }

  // Generate document hash helper
  const generateDocumentHash = async (content: string, path: string): Promise<string> => {
    const encoder = new TextEncoder()
    const data = encoder.encode(content + path)
    const hashBuffer = await crypto.subtle.digest('SHA-256', data)
    const hashArray = Array.from(new Uint8Array(hashBuffer))
    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('')
  }

  // Get confidence color
  const getConfidenceColor = (confidence: number): string => {
    if (confidence >= 0.8) return 'text-green-400'
    if (confidence >= 0.6) return 'text-yellow-400'
    return 'text-red-400'
  }

  // Get importance color
  const getImportanceColor = (importance: string): string => {
    switch (importance) {
      case 'critical': return 'text-red-400'
      case 'high': return 'text-orange-400'
      case 'medium': return 'text-yellow-400'
      case 'low': return 'text-gray-400'
      default: return 'text-gray-400'
    }
  }

  return (
    <div className="h-full bg-gray-900 border border-gray-700 rounded-lg overflow-hidden">
      {/* Header */}
      <div className="flex items-center justify-between px-4 py-3 bg-gray-800 border-b border-gray-700">
        <div className="flex items-center gap-2">
          <FontAwesomeIcon icon={ICONS.brain} className="text-primary text-sm" />
          <span className="text-sm font-medium text-supplement1">Smart Annotation</span>
          {uiState.confidenceScore > 0 && (
            <span className={`text-xs px-2 py-1 rounded ${getConfidenceColor(uiState.confidenceScore)}`}>
              {Math.round(uiState.confidenceScore * 100)}% confidence
            </span>
          )}
        </div>
        
        {/* Analysis Button */}
        <button
          onClick={handleSmartAnnotation}
          disabled={uiState.isAnalyzing || !fileContent}
          className="flex items-center gap-2 px-3 py-1 bg-primary hover:bg-primary/80 disabled:bg-gray-600 disabled:cursor-not-allowed text-gray-900 text-xs rounded transition-colors"
        >
          {uiState.isAnalyzing ? (
            <>
              <FontAwesomeIcon icon={ICONS.spinner} className="animate-spin" />
              Analyzing...
            </>
          ) : (
            <>
              <FontAwesomeIcon icon={ICONS.magic} />
              Analyze
            </>
          )}
        </button>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto p-3">
        {!selectedFile ? (
          <div className="flex items-center justify-center h-32">
            <div className="text-center">
              <FontAwesomeIcon icon={ICONS.brain} className="text-gray-400 text-2xl mb-2" />
              <p className="text-sm text-gray-400">Select a file to enable smart annotation</p>
            </div>
          </div>
        ) : uiState.suggestedTags.length === 0 && !uiState.isAnalyzing ? (
          <div className="text-center py-8">
            <FontAwesomeIcon icon={ICONS.lightbulb} className="text-gray-400 text-3xl mb-3" />
            <h3 className="text-sm font-medium text-supplement1 mb-2">Ready for Analysis</h3>
            <p className="text-xs text-gray-400 mb-4">
              Click "Analyze" to extract entities and insights from this document
            </p>
            <div className="text-xs text-gray-500">
              <div className="flex items-center justify-center gap-4">
                <span>• Entity Recognition</span>
                <span>• Key Insights</span>
                <span>• Smart Tagging</span>
              </div>
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            {/* Extracted Entities Section */}
            {uiState.suggestedTags.length > 0 && (
              <div className="bg-gray-800 rounded-lg overflow-hidden">
                <button
                  onClick={() => toggleSection('entities')}
                  className="w-full flex items-center justify-between p-3 hover:bg-gray-700 transition-colors"
                >
                  <div className="flex items-center gap-2">
                    <FontAwesomeIcon icon={ICONS.tags} className="text-primary text-sm" />
                    <span className="text-sm font-medium text-supplement1">
                      Extracted Entities ({uiState.suggestedTags.length})
                    </span>
                  </div>
                  <FontAwesomeIcon
                    icon={expandedSections.has('entities') ? ICONS.chevronUp : ICONS.chevronDown}
                    className="text-xs text-gray-400"
                  />
                </button>

                {expandedSections.has('entities') && (
                  <div className="px-3 pb-3">
                    <div className="space-y-2">
                      {uiState.suggestedTags.map((entity, index) => {
                        const isSelected = uiState.selectedTags.some(tag => tag.entity === entity.entity)
                        return (
                          <div
                            key={index}
                            onClick={() => handleEntityToggle(entity)}
                            className={`flex items-center justify-between p-2 rounded cursor-pointer transition-colors ${
                              isSelected 
                                ? 'bg-primary/20 border border-primary/30' 
                                : 'bg-gray-700 hover:bg-gray-600'
                            }`}
                          >
                            <div className="flex items-center gap-2">
                              <FontAwesomeIcon
                                icon={isSelected ? ICONS.checkSquare : ICONS.square}
                                className={`text-xs ${isSelected ? 'text-primary' : 'text-gray-400'}`}
                              />
                              <span className="text-xs text-supplement1">{entity.entity}</span>
                              <span className="text-xs px-1 py-0.5 bg-gray-600 rounded text-gray-300">
                                {entity.type}
                              </span>
                            </div>
                            <span className={`text-xs ${getConfidenceColor(entity.confidence)}`}>
                              {Math.round(entity.confidence * 100)}%
                            </span>
                          </div>
                        )
                      })}
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* Key Insights Section */}
            {uiState.insights.length > 0 && (
              <div className="bg-gray-800 rounded-lg overflow-hidden">
                <button
                  onClick={() => toggleSection('insights')}
                  className="w-full flex items-center justify-between p-3 hover:bg-gray-700 transition-colors"
                >
                  <div className="flex items-center gap-2">
                    <FontAwesomeIcon icon={ICONS.lightbulb} className="text-primary text-sm" />
                    <span className="text-sm font-medium text-supplement1">
                      Key Insights ({uiState.insights.length})
                    </span>
                  </div>
                  <FontAwesomeIcon
                    icon={expandedSections.has('insights') ? ICONS.chevronUp : ICONS.chevronDown}
                    className="text-xs text-gray-400"
                  />
                </button>

                {expandedSections.has('insights') && (
                  <div className="px-3 pb-3">
                    <div className="space-y-2">
                      {uiState.insights.map((insight, index) => (
                        <div key={index} className="p-2 bg-gray-700 rounded">
                          <div className="flex items-start justify-between mb-1">
                            <span className={`text-xs px-1 py-0.5 rounded ${getImportanceColor(insight.importance)}`}>
                              {insight.importance}
                            </span>
                            <span className={`text-xs ${getConfidenceColor(insight.confidence)}`}>
                              {Math.round(insight.confidence * 100)}%
                            </span>
                          </div>
                          <p className="text-xs text-supplement1">{insight.insight}</p>
                          {insight.supporting_entities && insight.supporting_entities.length > 0 && (
                            <div className="flex flex-wrap gap-1 mt-2">
                              {insight.supporting_entities.map((entity, entityIndex) => (
                                <span key={entityIndex} className="text-xs px-1 py-0.5 bg-gray-600 rounded text-gray-300">
                                  {entity}
                                </span>
                              ))}
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* Selected Tags Summary */}
            {uiState.selectedTags.length > 0 && (
              <div className="bg-gray-800 rounded-lg p-3">
                <div className="flex items-center gap-2 mb-2">
                  <FontAwesomeIcon icon={ICONS.bookmark} className="text-primary text-sm" />
                  <span className="text-sm font-medium text-supplement1">
                    Selected Tags ({uiState.selectedTags.length})
                  </span>
                </div>
                <div className="flex flex-wrap gap-1">
                  {uiState.selectedTags.map((tag, index) => (
                    <span key={index} className="text-xs px-2 py-1 bg-primary/20 border border-primary/30 rounded text-primary">
                      {tag.entity}
                    </span>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Footer */}
      {uiState.currentSession && (
        <div className="px-4 py-2 bg-gray-800 border-t border-gray-700">
          <div className="flex items-center justify-between text-xs text-gray-400">
            <span>Model: {config.ai_model}</span>
            <span>
              Processing: {uiState.currentSession.intelligence_session.processing_time_ms}ms
            </span>
          </div>
        </div>
      )}
    </div>
  )
}
