---
description:  This is to prevent duplication in variables
globs:
alwaysApply: false
---
**Requirement**: Prevent duplicate variables through systematic naming
**Pattern**: `[context]_[purpose]_[type]` or `[component][Purpose][Type]`
**Examples**:
- ✅ `messagePin_state`, `vaultSuggestion_modal`, `intelligence_extraction_data`
- ❌ `state`, `modal`, `data` (too generic, causes duplicates)
**Enforcement**:
- [ ] ESLint rule for variable naming patterns
- [ ] Pre-commit hook validation
- [ ] Code review checklist item