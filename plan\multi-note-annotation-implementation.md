# Multi-Note Annotation System Implementation Plan

## Overview
The smart annotation system supports multiple notes per document with navigation controls, allowing users to generate annotations from:
1. **Full Document Analysis** - AI analyzes entire document content
2. **Selected Text** - AI analyzes user-selected text portions  
3. **User Prompt** - AI responds to specific user questions about the document

## Data Architecture

### Core Principle: One Intelligence File Per Document
- **Path**: `.context/<document_hash>/intelligence.json`
- **Structure**: Single JSON file containing all intelligence data for one document
- **Benefits**: Atomic operations, easy backup, clear ownership

### Key Data Components

#### 1. Smart Annotations Array
```typescript
smart_annotations: SmartAnnotation[] = [
  {
    annotation_id: "note_001",
    note_number: 1,           // For "Note #1" display
    title: "Document Overview", // Auto-generated or user-defined
    content: "AI-generated insights...",
    generation_source: "full_document" | "selected_text" | "user_prompt",
    generation_prompt?: "Custom user question",
    // ... other fields
  }
]
```

#### 2. Navigation State
```typescript
annotation_navigation: {
  total_notes: 3,
  current_note_index: 2,    // 0-based index for "3 of 12"
  note_order: ["note_001", "note_002", "note_003"],
  last_viewed_note: "note_003"
}
```

#### 3. Entity Selections
```typescript
entity_selections: EntitySelection[] = [
  {
    entity_text: "UI Design",
    confidence: 0.95,
    is_selected: true,
    color_category: "primary", // High confidence
    rank: 1,                   // Top 3 auto-selected
    // ... other fields
  }
]
```

## UI Implementation Strategy

### 1. Multi-Note Navigation Header
```html
<!-- Note Header with Navigation -->
<div class="note-header">
  <div class="note-title">
    <span class="note-number">Note #3</span>
    <button class="delete-note">🗑️</button>
  </div>
  <div class="note-navigation">
    <button class="edit-note">✏️</button>
    <button class="print-note">🖨️</button>
    <span class="note-counter">3 of 12</span>
    <button class="prev-note">◀</button>
    <button class="next-note">▶</button>
  </div>
</div>
```

### 2. Note Content Area
```html
<!-- Scrollable Content with Document Summary -->
<div class="note-content">
  <div class="note-summary">
    <h3>Document Summary:</h3>
    <p>This comprehensive project specification document...</p>
  </div>
</div>
```

### 3. Note Generation Controls
```html
<!-- Generation Options -->
<div class="note-generation">
  <button class="analyze-full">Analyze Full Document</button>
  <button class="analyze-selection">Analyze Selected Text</button>
  <input class="custom-prompt" placeholder="Ask AI about this document...">
  <button class="generate-from-prompt">Generate</button>
</div>
```

## File Type Detection & Content Extraction

### 1. File Type Detection Logic
```typescript
const detectFileType = (fileName: string): FileTypeInfo => {
  const extension = fileName.split('.').pop()?.toLowerCase() || ''
  
  if (extension === 'pdf') {
    return {
      type: 'pdf',
      canExtractText: true,
      canAnnotate: true,
      requiresProcessing: true,
      extractionMethod: 'pdf-parse'
    }
  }
  
  if (['md', 'markdown'].includes(extension)) {
    return {
      type: 'markdown',
      canExtractText: true,
      canAnnotate: true,
      requiresProcessing: false,
      extractionMethod: 'direct-read'
    }
  }
  
  // ... other file types
}
```

### 2. Content Extraction for AI
```typescript
const extractContentForAI = async (filePath: string, fileType: FileTypeInfo) => {
  switch (fileType.type) {
    case 'pdf':
      // Use existing PDF plugin
      return await window.electronAPI.files.processFile(filePath, 'pdf')
    
    case 'markdown':
      // Direct file read + markdown parsing
      const content = await window.electronAPI.vault.readFile(filePath)
      return {
        text: content,
        metadata: { type: 'markdown', structure: parseMarkdownStructure(content) }
      }
    
    case 'text':
      // Direct file read
      return await window.electronAPI.vault.readFile(filePath)
    
    default:
      throw new Error(`Unsupported file type: ${fileType.type}`)
  }
}
```

## AI Integration Points

### 1. Full Document Analysis
```typescript
const analyzeFullDocument = async (content: string) => {
  const prompt = `Analyze this document and provide a comprehensive summary with key insights:`
  return await intelligenceService.generateAnnotation(content, prompt)
}
```

### 2. Selected Text Analysis  
```typescript
const analyzeSelectedText = async (selectedText: string, fullContent: string) => {
  const prompt = `Analyze this selected text in context of the full document:`
  return await intelligenceService.generateAnnotation(fullContent, prompt, selectedText)
}
```

### 3. User Prompt Analysis
```typescript
const analyzeWithPrompt = async (userPrompt: string, content: string) => {
  const prompt = `${userPrompt}\n\nBased on this document content:`
  return await intelligenceService.generateAnnotation(content, prompt)
}
```

## Storage Implementation

### 1. File Structure
```
.context/
├── <document_hash>/
│   ├── intelligence.json     # Main intelligence data
│   ├── sessions/            # Historical sessions
│   │   ├── session_20240115_120000.json
│   │   └── session_20240115_130000.json
│   └── backups/             # Automatic backups
│       └── intelligence_backup_20240115.json
```

### 2. Save Operations
```typescript
const saveIntelligenceData = async (data: FileIntelligenceData) => {
  // 1. Create backup of existing data
  await createBackup(data.document_hash)
  
  // 2. Save main intelligence file
  const filePath = `.context/${data.document_hash}/intelligence.json`
  await window.electronAPI.vault.writeFile(filePath, JSON.stringify(data, null, 2))
  
  // 3. Update navigation state
  data.annotation_navigation.total_notes = data.smart_annotations.length
  data.annotation_navigation.note_order = data.smart_annotations.map(a => a.annotation_id)
}
```

## User Interaction Flows

### 1. Create New Note from Full Document
1. User clicks "Analyze Full Document"
2. Extract document content based on file type
3. Send to AI model (Gemma3) for analysis
4. Create new SmartAnnotation with `generation_source: "full_document"`
5. Add to annotations array and update navigation
6. Save intelligence data

### 2. Create Note from Selected Text
1. User selects text in document viewer
2. User clicks "Analyze Selected Text"
3. Extract selected text + position info
4. Send to AI with context of full document
5. Create SmartAnnotation with `generation_source: "selected_text"`
6. Include `selected_text` and `position_info` fields

### 3. Create Note from User Prompt
1. User types question in prompt input
2. User clicks "Generate"
3. Send user prompt + document content to AI
4. Create SmartAnnotation with `generation_source: "user_prompt"`
5. Include `generation_prompt` field

### 4. Navigate Between Notes
1. User clicks prev/next buttons
2. Update `current_note_index` in navigation
3. Display corresponding note content
4. Update "X of Y" counter

This implementation provides a comprehensive multi-note annotation system that captures user intent, document insights, and maintains proper navigation state for an intuitive user experience.
