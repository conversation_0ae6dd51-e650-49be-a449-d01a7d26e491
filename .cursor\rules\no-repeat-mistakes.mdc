---
description:  This is to prevent repeating the same mistakes in dead loop
alwaysApply: true
---
Mistake Pattern Recognition
**Requirement**: Learn from recurring issues to prevent repetition
**Common Patterns Identified**:
- Duplicate variable names
- Missing type definitions
- FontAwesome import inconsistencies
- External dependency bloat
- Repeat pattern of cache version not being detected, make regular purge of cache
- Everytime the code required electron rebuild, but no actions for me.
- If same mistake happens over 2 times, stop and notify me
**Prevention**:
- [ ] Pattern-based linting rules
- [ ] Automated pattern detection
- [ ] Developer education on common mistakes