Short term memory - even though the agent has the codebase, and even I post the request_id to let agent to understand the context, the agent still forget the context very easily.  I need to figure out a way to let the agent to remember the context.  
I am thinking to let the agent to create a context memory for each request, and let the agent to reference the context memory when it is needed.
Rules setting is not "Always" following, even though I set so.
Long and complex tasks are not handled well.  Easy to quit.
Hallucation still a problem

> Case: Developed a rule to follow the UI by providing html, but the agent changed all the well-done UI because the complext tasks (lack of service, lack of )

```
Let me first examine the original UI design specification to understand what was intended, then analyze what we currently have and explain the discrepancies.

Now let me examine the current FileDetailsOverlay component to see what we have:

Now let me check the current TypeScript errors to understand the scope of the problem:

Now I understand the issue. Let me explain what happened and provide a step-by-step plan to fix this:

## Analysis: Why the Functions Don't Work and UI Was Modified

### **Root Cause Analysis:**

1. **Critical TypeScript Errors (99 errors across 25 files)**: The app cannot run properly due to TypeScript compilation failures, which violates Rule 2.2 from the development standards (zero tolerance for TypeScript errors).

2. **Interface Mismatches**: The biggest issue is in `FilePicker.tsx` (36 errors) where there's a mismatch between the `FileRecord` interface and actual usage:
   - Code uses `file_path` but interface expects `filepath`
   - Code uses `file_hash` but interface doesn't have this property
   - Code uses `indexed_at` but interface doesn't have this property
   - Metadata type mismatch (object vs string)

3. **Missing API Methods**: Several services reference API methods that don't exist:
   - `indexVaultFile`, `testDirectParsing`, `processFile`, `saveContentAsFile`, `getFileUrl`, `openPath`

4. **UI Modifications**: The current implementation doesn't match the original HTML design specification because:
   - **Layout Structure**: The original design has a specific 3-panel layout (file tree, document viewer, intelligence panel) but our implementation uses a different structure
   - **Styling**: Missing exact CSS classes and styling from the original design
   - **Component Integration**: The components were built separately but not integrated to match the exact HTML layout

### **Data Structure Issues:**

Looking at the original specification, the JSON structure for intelligence data should be:
```json
{
  "session_id": "uuid-v4",
  "document": { "path": "/vault/documents/project-spec.pdf", ... },
  "intelligence_session": {
    "extracted_entities": [
      {
        "entity": "UI Design",
        "type": "content_category", 
        "confidence": 0.95,
        "user_selected": true,
        "context": "Document contains extensive UI design specifications"
      }
    ]
  },
  "user_interactions": [...],
  "context_signals": {...},
  "learning_data": {...}
}


But our current implementation uses a different structure that doesn't match this specification.

## **Step-by-Step Implementation Plan:**

### **Phase 1: Fix Critical TypeScript Errors (PRIORITY 1)**

1. **Fix FileRecord Interface Mismatches**
   - Update `FileRecord` interface to include missing properties
   - Or update all usages to match existing interface
   - Fix metadata type (object vs string)

2. **Fix Missing API Methods**
   - Add missing methods to electronAPI interface
   - Or remove/replace calls to non-existent methods

3. **Clean Up Unused Imports and Variables**
   - Remove unused imports and variables to reduce error count

### **Phase 2: Restore Original UI Layout**

1. **Recreate Exact HTML Structure**
   - Follow the original HTML design specification exactly
   - Use the exact CSS classes and styling from the specification
   - Implement the 3-panel layout: file tree (20%) + document viewer (50%) + intelligence panel (30%)

2. **Fix Component Integration**
   - Ensure DocumentViewer matches the original PDF viewer design
   - Ensure EntityLabelSystem matches the original tags section
   - Ensure SmartAnnotationSystem matches the original annotation area

### **Phase 3: Implement Missing Functionality**

1. **Entity Label System**
   - Rank entities based on probability
   - Pre-select top 3 with different colors (primary, secondary, tertiary)
   - Add click interaction to select/unselect
   - Store selections in JSON format in `.intelligence` folder

2. **Smart Annotation System**
   - Make scrollable annotation area
   - Integrate with local AI for content analysis
   - Enable edit functionality for user input
   - Enable text selection from document viewer

3. **Document Viewer**
   - Support PDF, text, markdown, and image files
   - Add text selection capability
   - Integrate with annotation system

4. **Ask AI Navigation**
   - Navigate to Chat page with selected context vault
   - Attach parsed document to new chat
   - Preserve selected entities and annotations

### **Phase 4: Data Storage Implementation**

1. **JSON Data Structure**
   - Implement the exact JSON structure from specification
   - Store in `.intelligence/<document_hash>/` folders
   - Create session files, entity index, and learning patterns

2. **File Intelligence Service**
   - Complete the missing service methods
   - Implement proper error handling
   - Add data persistence and retrieval

Let me start by fixing the most critical TypeScript errors first, then we can restore the original UI layout and functionality.

**Would you like me to proceed with Phase 1 (fixing TypeScript errors) first, or would you prefer me to start with Phase 2 (restoring the original UI layout)?**
```
