import { BaseService, ServiceError, ServiceErrorCode } from './base'

// Simple toast service for notifications
const toastService = {
  success: (title: string, message: string) => {
    console.log(`✅ ${title}: ${message}`)
  },
  error: (title: string, message: string) => {
    console.error(`❌ ${title}: ${message}`)
  }
}

/**
 * Ask AI Navigation Service
 * Handles navigation to Chat page with document context and vault selection
 */
class AskAINavigationService extends BaseService {
  
  constructor() {
    super({
      name: 'AskAINavigationService',
      autoInitialize: true
    })
  }

  protected async doInitialize(): Promise<void> {
    // Initialize Ask AI Navigation service
    this.logger.info('Ask AI Navigation Service initialized')
  }

  /**
   * Navigate to chat with document and context vault
   */
  async navigateToChat(
    filePath: string, 
    fileName: string, 
    vaultName: string, 
    contextId?: string
  ): Promise<boolean> {
    const result = await this.executeOperation(
      'navigateToChat',
      async () => {
        // Create a new conversation with descriptive title
        const conversationTitle = `Chat with ${fileName}`
        
        if (window.electronAPI?.db?.createConversation) {
          const conversationId = await window.electronAPI.db.createConversation(conversationTitle)
          
          // Process the file for attachment if possible
          let processedFile = null
          if (window.electronAPI?.files?.processFile) {
            try {
              const processResult = await window.electronAPI.files.processFile(filePath)
              if (processResult.success) {
                processedFile = processResult
              }
            } catch (error) {
              this.logger.warn('Failed to process file for attachment', 'navigateToChat', error)
            }
          }

          // Build chat URL with parameters
          const params = new URLSearchParams()
          params.set('conversation', conversationId)
          
          if (contextId) {
            params.set('context', contextId)
          }
          
          if (processedFile) {
            params.set('file', filePath)
            params.set('filename', fileName)
          }

          // Add vault information for context
          params.set('vault', vaultName)
          
          // Navigate to chat page
          const chatUrl = `/chat?${params.toString()}`
          
          // Use window.location for navigation to ensure proper page load
          window.location.href = chatUrl
          
          return true
        } else {
          // Fallback navigation without database
          const params = new URLSearchParams()
          if (contextId) {
            params.set('context', contextId)
          }
          params.set('file', filePath)
          params.set('filename', fileName)
          params.set('vault', vaultName)
          
          const chatUrl = `/chat?${params.toString()}`
          window.location.href = chatUrl
          
          return true
        }
      }
    )

    if (!result.success) {
      this.logger.error('Failed to navigate to chat', 'navigateToChat', result.error)
      toastService.error('Navigation Failed', 'Could not open chat with document')
      return false
    }

    return result.data!
  }

  /**
   * Navigate to chat with selected text context
   */
  async navigateToChatWithText(
    filePath: string,
    fileName: string,
    vaultName: string,
    selectedText: string,
    contextId?: string
  ): Promise<boolean> {
    const result = await this.executeOperation(
      'navigateToChatWithText',
      async () => {
        // Create conversation with text context
        const conversationTitle = `Discuss: "${selectedText.substring(0, 50)}${selectedText.length > 50 ? '...' : ''}"`
        
        if (window.electronAPI?.db?.createConversation) {
          const conversationId = await window.electronAPI.db.createConversation(conversationTitle)
          
          // Build chat URL with text context
          const params = new URLSearchParams()
          params.set('conversation', conversationId)
          
          if (contextId) {
            params.set('context', contextId)
          }
          
          params.set('file', filePath)
          params.set('filename', fileName)
          params.set('vault', vaultName)
          params.set('selectedText', selectedText)
          
          const chatUrl = `/chat?${params.toString()}`
          window.location.href = chatUrl
          
          return true
        } else {
          // Fallback navigation
          const params = new URLSearchParams()
          if (contextId) {
            params.set('context', contextId)
          }
          params.set('file', filePath)
          params.set('filename', fileName)
          params.set('vault', vaultName)
          params.set('selectedText', selectedText)
          
          const chatUrl = `/chat?${params.toString()}`
          window.location.href = chatUrl
          
          return true
        }
      }
    )

    if (!result.success) {
      this.logger.error('Failed to navigate to chat with text', 'navigateToChatWithText', result.error)
      toastService.error('Navigation Failed', 'Could not open chat with selected text')
      return false
    }

    return result.data!
  }

  /**
   * Navigate to chat with entity context
   */
  async navigateToChatWithEntities(
    filePath: string,
    fileName: string,
    vaultName: string,
    selectedEntities: string[],
    contextId?: string
  ): Promise<boolean> {
    const result = await this.executeOperation(
      'navigateToChatWithEntities',
      async () => {
        const entitiesText = selectedEntities.join(', ')
        const conversationTitle = `Explore: ${entitiesText.substring(0, 50)}${entitiesText.length > 50 ? '...' : ''}`
        
        if (window.electronAPI?.db?.createConversation) {
          const conversationId = await window.electronAPI.db.createConversation(conversationTitle)
          
          // Build chat URL with entity context
          const params = new URLSearchParams()
          params.set('conversation', conversationId)
          
          if (contextId) {
            params.set('context', contextId)
          }
          
          params.set('file', filePath)
          params.set('filename', fileName)
          params.set('vault', vaultName)
          params.set('entities', JSON.stringify(selectedEntities))
          
          const chatUrl = `/chat?${params.toString()}`
          window.location.href = chatUrl
          
          return true
        } else {
          // Fallback navigation
          const params = new URLSearchParams()
          if (contextId) {
            params.set('context', contextId)
          }
          params.set('file', filePath)
          params.set('filename', fileName)
          params.set('vault', vaultName)
          params.set('entities', JSON.stringify(selectedEntities))
          
          const chatUrl = `/chat?${params.toString()}`
          window.location.href = chatUrl
          
          return true
        }
      }
    )

    if (!result.success) {
      this.logger.error('Failed to navigate to chat with entities', 'navigateToChatWithEntities', result.error)
      toastService.error('Navigation Failed', 'Could not open chat with selected entities')
      return false
    }

    return result.data!
  }

  /**
   * Navigate to chat with annotation context
   */
  async navigateToChatWithAnnotation(
    filePath: string,
    fileName: string,
    vaultName: string,
    annotation: string,
    contextId?: string
  ): Promise<boolean> {
    const result = await this.executeOperation(
      'navigateToChatWithAnnotation',
      async () => {
        const conversationTitle = `Discuss annotation: ${annotation.substring(0, 50)}${annotation.length > 50 ? '...' : ''}`
        
        if (window.electronAPI?.db?.createConversation) {
          const conversationId = await window.electronAPI.db.createConversation(conversationTitle)
          
          // Build chat URL with annotation context
          const params = new URLSearchParams()
          params.set('conversation', conversationId)
          
          if (contextId) {
            params.set('context', contextId)
          }
          
          params.set('file', filePath)
          params.set('filename', fileName)
          params.set('vault', vaultName)
          params.set('annotation', annotation)
          
          const chatUrl = `/chat?${params.toString()}`
          window.location.href = chatUrl
          
          return true
        } else {
          // Fallback navigation
          const params = new URLSearchParams()
          if (contextId) {
            params.set('context', contextId)
          }
          params.set('file', filePath)
          params.set('filename', fileName)
          params.set('vault', vaultName)
          params.set('annotation', annotation)
          
          const chatUrl = `/chat?${params.toString()}`
          window.location.href = chatUrl
          
          return true
        }
      }
    )

    if (!result.success) {
      this.logger.error('Failed to navigate to chat with annotation', 'navigateToChatWithAnnotation', result.error)
      toastService.error('Navigation Failed', 'Could not open chat with annotation')
      return false
    }

    return result.data!
  }

  /**
   * Show success notification for navigation
   */
  showNavigationSuccess(fileName: string): void {
    toastService.success(
      'Opening Chat',
      `Starting conversation with ${fileName}`
    )
  }
}

export const askAINavigationService = new AskAINavigationService()
