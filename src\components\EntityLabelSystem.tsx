import React, { useState, useEffect } from 'react'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { ICONS } from './Icons/index'
import { EntitySelection } from '../types/intelligenceTypes'

interface EntityLabelSystemProps {
  entities: EntitySelection[]
  onEntityToggle: (entityId: string, isSelected: boolean) => void
  onEntitiesChange: (entities: EntitySelection[]) => void
  isLoading?: boolean
}

export const EntityLabelSystem: React.FC<EntityLabelSystemProps> = ({
  entities,
  onEntityToggle,
  onEntitiesChange,
  isLoading = false
}) => {
  const [localEntities, setLocalEntities] = useState<EntitySelection[]>(entities)

  useEffect(() => {
    setLocalEntities(entities)
  }, [entities])

  const handleEntityClick = (entityId: string) => {
    const updatedEntities = localEntities.map(entity => {
      if (entity.entity_id === entityId) {
        const newSelected = !entity.is_selected
        onEntityToggle(entityId, newSelected)
        return {
          ...entity,
          is_selected: newSelected,
          selection_timestamp: newSelected ? new Date().toISOString() : entity.selection_timestamp
        }
      }
      return entity
    })
    
    setLocalEntities(updatedEntities)
    onEntitiesChange(updatedEntities)
  }

  const getEntityColorClasses = (entity: EntitySelection) => {
    if (!entity.is_selected) {
      return 'bg-gray-700 text-gray-300 border-gray-600 hover:bg-gray-600'
    }

    switch (entity.color_category) {
      case 'primary':
        return 'bg-primary/20 text-primary border-primary/30 hover:bg-primary/30'
      case 'secondary':
        return 'bg-secondary/20 text-secondary border-secondary/30 hover:bg-secondary/30'
      case 'tertiary':
        return 'bg-supplement2/20 text-supplement2 border-supplement2/30 hover:bg-supplement2/30'
      default:
        return 'bg-supplement1/20 text-supplement1 border-supplement1/30 hover:bg-supplement1/30'
    }
  }

  const getConfidenceIcon = (confidence: number) => {
    if (confidence >= 0.9) return ICONS.star
    if (confidence >= 0.8) return ICONS.starHalfStroke
    return ICONS.starRegular
  }

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.9) return 'text-yellow-400'
    if (confidence >= 0.8) return 'text-yellow-500'
    return 'text-gray-400'
  }

  // Sort entities by rank (confidence)
  const sortedEntities = [...localEntities].sort((a, b) => a.rank - b.rank)

  if (isLoading) {
    return (
      <div className="p-3 border-b border-tertiary/50">
        <p className="text-xs text-gray-400 mb-2">Analyzing document entities...</p>
        <div className="flex flex-wrap gap-1 mb-2">
          {[1, 2, 3, 4, 5].map(i => (
            <div
              key={i}
              className="px-2 py-0.5 bg-gray-700/50 text-gray-400 text-xs rounded-full border border-gray-600 animate-pulse"
            >
              <div className="w-16 h-3 bg-gray-600 rounded"></div>
            </div>
          ))}
        </div>
        <div className="flex items-center gap-1 text-xs text-gray-500">
          <FontAwesomeIcon icon={ICONS.wandMagicSparkles} className="animate-spin" />
          <span>AI is discovering key concepts...</span>
        </div>
      </div>
    )
  }

  return (
    <div className="p-3 border-b border-tertiary/50">
      <div className="flex items-center justify-between mb-2">
        <p className="text-xs text-gray-400">Select key ideas to enhance AI context learning</p>
        <div className="flex items-center gap-1 text-xs text-gray-500">
          <FontAwesomeIcon icon={ICONS.lightbulb} />
          <span>{localEntities.filter(e => e.is_selected).length} selected</span>
        </div>
      </div>
      
      <div className="flex flex-wrap gap-1 mb-2">
        {sortedEntities.map((entity) => (
          <button
            key={entity.entity_id}
            onClick={() => handleEntityClick(entity.entity_id)}
            className={`
              px-2 py-0.5 text-xs rounded-full border font-medium transition-all duration-200 
              flex items-center gap-1 hover:scale-105 active:scale-95
              ${getEntityColorClasses(entity)}
            `}
            title={`${entity.entity_text} (${Math.round(entity.confidence * 100)}% confidence)\n${entity.context_snippet}`}
          >
            <FontAwesomeIcon 
              icon={getConfidenceIcon(entity.confidence)} 
              className={`text-xs ${getConfidenceColor(entity.confidence)}`}
            />
            <span>{entity.entity_text}</span>
            {entity.is_selected && (
              <FontAwesomeIcon icon={ICONS.check} className="text-xs" />
            )}
          </button>
        ))}
      </div>

      {/* Entity Statistics */}
      <div className="flex items-center justify-between text-xs text-gray-500">
        <div className="flex items-center gap-3">
          <span className="flex items-center gap-1">
            <div className="w-2 h-2 bg-primary rounded-full"></div>
            High confidence
          </span>
          <span className="flex items-center gap-1">
            <div className="w-2 h-2 bg-secondary rounded-full"></div>
            Medium confidence
          </span>
          <span className="flex items-center gap-1">
            <div className="w-2 h-2 bg-supplement2 rounded-full"></div>
            Lower confidence
          </span>
        </div>
        <button 
          className="text-supplement2 hover:text-supplement1 transition-colors flex items-center gap-1"
          onClick={() => {
            // Reset all selections
            const resetEntities = localEntities.map(entity => ({
              ...entity,
              is_selected: false
            }))
            setLocalEntities(resetEntities)
            onEntitiesChange(resetEntities)
          }}
        >
          <FontAwesomeIcon icon={ICONS.arrowRotateLeft} />
          Reset
        </button>
      </div>

      {/* Selected Entities Summary */}
      {localEntities.some(e => e.is_selected) && (
        <div className="mt-2 p-2 bg-gray-800/50 rounded border border-gray-700">
          <p className="text-xs text-gray-400 mb-1">Selected for context:</p>
          <div className="flex flex-wrap gap-1">
            {localEntities
              .filter(e => e.is_selected)
              .sort((a, b) => a.rank - b.rank)
              .map(entity => (
                <span
                  key={entity.entity_id}
                  className="px-1.5 py-0.5 bg-primary/10 text-primary text-xs rounded border border-primary/20"
                >
                  {entity.entity_text}
                </span>
              ))}
          </div>
        </div>
      )}
    </div>
  )
}
