import React, { useState } from 'react'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { ICONS } from './Icons/index'
import { DocumentViewer } from './DocumentViewer'

interface FileDetailsOverlayProps {
  filePath?: string
  fileName?: string
  onClose: () => void
}

const availableTags = [
  'UI Design', 'Specifications', 'Requirements', 'Components', 'Wireframes', 'Architecture', 'Testing', 'Documentation'
]

const examplePrompts = [
  'Summarize the second paragraph',
  'Extract all color codes and create a palette reference',
  'List all component specifications with their properties',
  'Identify potential implementation challenges and solutions',
  'Compare this with existing design systems and best practices',
  'Generate test cases for accessibility compliance'
]

export const FileDetailsOverlay: React.FC<FileDetailsOverlayProps> = ({
  filePath,
  fileName: propFileName,
  onClose
}) => {
  const [selectedTags, setSelectedTags] = useState<Set<string>>(new Set(['UI Design', 'Specifications', 'Requirements']))
  const [currentNoteIndex, setCurrentNoteIndex] = useState(3)
  const totalNotes = 12

  if (!filePath) return null
  const fileName = propFileName || filePath.split('/').pop() || filePath

  // Tag color mapping
  const tagColorClass = (tag: string) => {
    if (tag === 'UI Design') return 'bg-primary/20 text-primary border-primary/30'
    if (tag === 'Specifications') return 'bg-secondary/20 text-secondary border-secondary/30'
    if (tag === 'Requirements') return 'bg-supplement2/20 text-supplement2 border-supplement2/30'
    return 'bg-gray-700 text-gray-300'
  }

  // Bullet color mapping
  const bulletColorClass = (idx: number) => {
    if (idx === 0) return 'text-primary'
    if (idx === 1) return 'text-secondary'
    if (idx === 2) return 'text-supplement2'
    return 'text-primary'
  }

  return (
    <div className="absolute inset-0 bg-gray-900 z-50 flex font-sans">
      {/* Center Column - PDF Viewer */}
      <div className="flex-1 bg-gray-800 flex flex-col border-r border-tertiary/50">
        {/* PDF Viewer Header */}
        <div className="p-4 border-b border-tertiary/50 flex items-center justify-between">
          <div className="flex items-center gap-2">
            <button onClick={onClose} className="p-2 hover:bg-gray-700 rounded-lg transition-colors">
              <FontAwesomeIcon icon={ICONS.arrowLeft} className="text-gray-400 text-sm" />
            </button>
            <FontAwesomeIcon icon={ICONS.filePdf} className="text-secondary text-lg" />
            <span className="text-supplement1 font-semibold text-lg">{fileName}</span>
          </div>
          <div className="flex items-center gap-2">
            <button className="p-2 hover:bg-gray-700 rounded-lg transition-colors">
              <FontAwesomeIcon icon={ICONS.searchMinus} className="text-gray-400 text-sm" />
            </button>
            <span className="text-gray-400 text-sm">100%</span>
            <button className="p-2 hover:bg-gray-700 rounded-lg transition-colors">
              <FontAwesomeIcon icon={ICONS.searchPlus} className="text-gray-400 text-sm" />
            </button>
            <button className="p-2 hover:bg-gray-700 rounded-lg transition-colors">
              <FontAwesomeIcon icon={ICONS.download} className="text-gray-400 text-sm" />
            </button>
          </div>
        </div>
        {/* Document Viewer */}
        <DocumentViewer filePath={filePath} fileName={fileName} />
      </div>

      {/* Right Column - File Details */}
      <div className="w-1/3 bg-gray-800 flex flex-col overflow-hidden min-w-[320px] max-w-[480px]" style={{ resize: 'horizontal' }}>
        {/* File Header */}
        <div className="p-4 border-b border-tertiary/50 flex items-center justify-between">
          <div className="flex items-center gap-2 flex-1 min-w-0">
            <FontAwesomeIcon icon={ICONS.filePdf} className="text-secondary text-lg flex-shrink-0" />
            <span className="text-supplement1 font-semibold text-sm truncate">{fileName}</span>
          </div>
          <div className="flex items-center gap-1 ml-2">
            <button className="p-1 hover:bg-gray-700 rounded transition-colors group relative">
              <FontAwesomeIcon icon={ICONS.lock} className="text-red-500 text-sm" />
              <div className="absolute bottom-6 right-0 bg-gray-700 text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap z-10">
                Private Document
              </div>
            </button>
            <button className="p-1 hover:bg-gray-700 rounded transition-colors group relative">
              <FontAwesomeIcon icon={ICONS.ellipsisVertical} className="text-gray-400 text-sm" />
              <div className="absolute bottom-6 right-0 bg-gray-700 text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap z-10">
                System Operations
              </div>
            </button>
            <button onClick={onClose} className="p-1 hover:bg-red-600 rounded transition-colors">
              <FontAwesomeIcon icon={ICONS.xmark} className="text-gray-400 text-sm" />
            </button>
          </div>
        </div>

        {/* Tags Section */}
        <div className="p-3 border-b border-tertiary/50">
          <p className="text-xs text-gray-400 mb-2">Select any key ideas about this doc to enhance the AI context learning.</p>
          <div className="flex flex-wrap gap-1 mb-2">
            {availableTags.map((tag) => (
              <span
                key={tag}
                onClick={() => {
                  const newSelected = new Set(selectedTags)
                  if (newSelected.has(tag)) newSelected.delete(tag)
                  else newSelected.add(tag)
                  setSelectedTags(newSelected)
                }}
                className={`px-2 py-0.5 text-xs rounded-full border font-medium cursor-pointer transition-colors ${tagColorClass(tag)} ${selectedTags.has(tag) ? '' : 'hover:bg-gray-600'}`}
              >
                {tag}
              </span>
            ))}
            <button className="px-2 py-0.5 border border-dashed border-gray-600 text-gray-400 text-xs rounded-full hover:border-gray-500 transition-colors flex items-center">
              <FontAwesomeIcon icon={ICONS.plus} className="mr-1" />
              Add
            </button>
          </div>
        </div>

        {/* Document Summary & Notes */}
        <div className="p-3 border-b border-tertiary/50 flex-1 overflow-y-auto">
          {/* Note Navigation */}
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center gap-2">
              <h3 className="text-supplement1 font-semibold text-sm">Note #{currentNoteIndex}</h3>
              <button className="p-1 hover:bg-gray-700 rounded transition-colors group relative">
                <FontAwesomeIcon icon={ICONS.trash} className="text-red-500 text-xs" />
                <div className="absolute bottom-6 left-0 bg-gray-700 text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap z-10">
                  Delete Note
                </div>
              </button>
            </div>
            <div className="flex items-center gap-2">
              <div className="flex items-center gap-1">
                <button className="p-1 hover:bg-gray-700 rounded transition-colors group relative">
                  <FontAwesomeIcon icon={ICONS.edit} className="text-supplement2 text-xs" />
                  <div className="absolute bottom-6 left-0 bg-gray-700 text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap z-10">
                    Edit Tags
                  </div>
                </button>
                <button className="p-1 hover:bg-gray-700 rounded transition-colors group relative">
                  <FontAwesomeIcon icon={ICONS.save} className="text-primary text-xs" />
                  <div className="absolute bottom-6 left-0 bg-gray-700 text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap z-10">
                    Save Changes
                  </div>
                </button>
              </div>
              <div className="flex items-center gap-1 text-xs text-gray-400">
                <span>{currentNoteIndex} of {totalNotes}</span>
                <button
                  onClick={() => setCurrentNoteIndex(Math.max(1, currentNoteIndex - 1))}
                  className="p-0.5 hover:bg-gray-700 rounded transition-colors"
                >
                  <FontAwesomeIcon icon={ICONS.chevronLeft} className="text-xs" />
                </button>
                <button
                  onClick={() => setCurrentNoteIndex(Math.min(totalNotes, currentNoteIndex + 1))}
                  className="p-0.5 hover:bg-gray-700 rounded transition-colors"
                >
                  <FontAwesomeIcon icon={ICONS.chevronRight} className="text-xs" />
                </button>
              </div>
            </div>
          </div>

          {/* Summary Box */}
          <div className="bg-gray-900 rounded-lg p-3 border border-tertiary/50 mb-3">
            <div className="text-sm text-gray-300 leading-relaxed">
              <div className="">Document Summary:<br /><br /></div>
              This comprehensive project specification document outlines the complete design system requirements for the Chatlo application. It includes detailed UI components, color schemes, typography guidelines, and interaction patterns.
              <p className="mt-2 mb-2">The document covers 47 pages of technical specifications including:</p>
              <ul className="text-gray-300 text-xs space-y-1 mb-2 pl-3">
                {[
                  'Component library specifications (buttons, forms, navigation)',
                  'Color palette and theming guidelines',
                  'Responsive design breakpoints and layouts',
                  'Accessibility requirements and testing protocols'
                ].map((item, idx) => (
                  <li className="flex items-start gap-1" key={item}>
                    <span className={`${bulletColorClass(idx)} mt-0.5 text-xs`}>•</span>
                    <span>{item}</span>
                  </li>
                ))}
              </ul>
              <p className="text-gray-400 text-xs">
                Last updated: January 15, 2024 • 2.1 MB • 47 pages
              </p>
            </div>
          </div>

          {/* Example Prompts */}
          <div className="mb-3">
            <h4 className="text-supplement1 font-medium text-xs mb-2">Example Annotation Prompts</h4>
            <div className="space-y-1">
              {examplePrompts.map((prompt) => (
                <button
                  key={prompt}
                  className="w-full text-left p-1.5 bg-gray-900/50 hover:bg-gray-900 rounded text-xs text-gray-300 border border-tertiary/30 hover:border-tertiary/50 transition-colors leading-tight"
                >
                  {`"${prompt}"`}
                </button>
              ))}
            </div>
          </div>

          {/* Custom Prompt Input */}
          <div className="mb-3">
            <textarea
              placeholder="Enter your custom annotation prompt here..."
              className="w-full bg-gray-900 border border-tertiary/50 rounded-lg p-2 text-xs text-gray-300 placeholder-gray-500 resize-none focus:outline-none focus:border-primary/50 transition-colors"
              rows={3}
            />
          </div>
        </div>

        {/* Bottom Action Buttons */}
        <div className="p-3 border-t border-tertiary/50 space-y-2">
          <button className="w-full p-3 bg-secondary hover:bg-secondary/80 text-gray-900 rounded-lg transition-colors flex items-center justify-center gap-2">
            <FontAwesomeIcon icon={ICONS.comment} className="text-sm" />
            Ask AI
            <span className="text-xs ml-auto">Send to chat to explore more with other LLM models</span>
          </button>
          <button className="w-full p-2 bg-gray-700 hover:bg-gray-600 text-supplement1 rounded-lg transition-colors flex items-center justify-center gap-2">
            <FontAwesomeIcon icon={ICONS.fileExport} className="text-sm" />
            Extract Text
            <span className="text-xs ml-auto">OCR the text and copy to the clipboard</span>
          </button>
          <div className="text-xs text-gray-500 text-center mt-2">
            Vault: Your First Context Vault
          </div>
        </div>
      </div>
    </div>
  )
}