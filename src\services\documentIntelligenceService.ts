/**
 * Document Intelligence Collection Service
 * Integrates with existing ChatLo architecture for smart document analysis
 */

import { BaseService } from './base'
import { 
  DocumentIntelligenceSession, 
  IntelligenceAnalysis, 
  ExtractedEntity, 
  KeyInsight,
  DocumentMetadata,

  SmartAnnotationConfig,
  IntelligenceCollectionService,
  EntityType,
  UserInteraction
} from '../types/intelligenceTypes'

import { intelligenceService } from './intelligenceService'

class DocumentIntelligenceService extends BaseService implements IntelligenceCollectionService {
  private currentSession: DocumentIntelligenceSession | null = null
  private sessionCache = new Map<string, DocumentIntelligenceSession[]>()
  private defaultConfig: SmartAnnotationConfig = {
    ai_model: 'gemma3-32k',
    confidence_threshold: 0.7,
    max_entities: 20,
    enable_relationships: true,
    auto_save: true,
    include_content_analysis: true
  }

  constructor() {
    super({
      name: 'DocumentIntelligenceService',
      autoInitialize: true
    })
  }

  protected async doInitialize(): Promise<void> {
    this.logger.info('Document Intelligence Service initialized')
  }

  /**
   * Main entry point for document analysis
   */
  async analyzeDocument(
    filePath: string, 
    content: string, 
    config?: SmartAnnotationConfig
  ): Promise<IntelligenceAnalysis> {
    return await this.executeOperationOrThrow(
      'analyzeDocument',
      async () => {
        const analysisConfig = { ...this.defaultConfig, ...config }
        const startTime = Date.now()

        this.logger.info('Starting document analysis', 'analyzeDocument', {
          filePath,
          contentLength: content.length,
          model: analysisConfig.ai_model
        })

        // Generate document hash for identification
        // Generate document hash for session identification
        await this.generateDocumentHash(content, filePath)
        
        // Extract entities using AI model
        const entities = await this.extractEntitiesWithAI(content, analysisConfig)
        
        // Generate insights based on entities and content
        const insights = await this.generateInsights(content, entities)
        
        // Calculate overall confidence
        const confidence = this.calculateOverallConfidence(entities, insights)
        
        // Find related documents
        const relatedDocs = await this.findRelatedDocuments(entities, filePath)

        const processingTime = Date.now() - startTime

        const analysis: IntelligenceAnalysis = {
          session_type: 'smart_annotation',
          ai_model: analysisConfig.ai_model,
          processing_time_ms: processingTime,
          confidence_score: confidence,
          extracted_entities: entities,
          key_insights: insights,
          content_summary: await this.generateContentSummary(content, entities),
          related_documents: relatedDocs
        }

        this.logger.info('Document analysis completed', 'analyzeDocument', {
          entitiesCount: entities.length,
          insightsCount: insights.length,
          confidence,
          processingTime
        })

        return analysis
      },
      { filePath, contentLength: content.length }
    )
  }

  /**
   * Extract entities using AI model integration
   */
  private async extractEntitiesWithAI(
    content: string, 
    config: SmartAnnotationConfig
  ): Promise<ExtractedEntity[]> {
    try {
      // Use existing intelligence service for AI model integration
      const extractionResult = await intelligenceService.extractIntelligence(content)
      
      // Convert to our entity format
      const entities: ExtractedEntity[] = []
      
      // Process entities from intelligence service
      if (extractionResult.data.entities) {
        extractionResult.data.entities.forEach(entity => {
          entities.push({
            entity: entity.text || '',
            type: this.mapEntityType(entity.type),
            confidence: entity.confidence || 0.5,
            user_selected: false,
            context: `Extracted from document content`,
            category: entity.type || 'general'
          })
        })
      }

      // Process topics as entities
      if (extractionResult.data.topics) {
        extractionResult.data.topics.forEach(topic => {
          entities.push({
            entity: topic.name,
            type: 'content_category',
            confidence: topic.relevance || 0.5,
            user_selected: false,
            context: `Topic identified in content`,
            category: topic.name || 'general'
          })
        })
      }

      // Sort by confidence and limit results
      return entities
        .sort((a, b) => b.confidence - a.confidence)
        .slice(0, config.max_entities)
        .filter(entity => entity.confidence >= config.confidence_threshold)

    } catch (error) {
      this.logger.error('AI entity extraction failed', 'extractEntitiesWithAI', error as Error)
      
      // Fallback to keyword-based extraction
      return this.extractEntitiesKeywordBased(content, config)
    }
  }

  /**
   * Fallback keyword-based entity extraction
   */
  private extractEntitiesKeywordBased(
    content: string, 
    config: SmartAnnotationConfig
  ): ExtractedEntity[] {
    const entities: ExtractedEntity[] = []
    
    // Define keyword patterns for different entity types
    const patterns = {
      technical_concept: /\b(API|SDK|framework|library|component|service|database|algorithm)\b/gi,
      action_item: /\b(TODO|FIXME|implement|create|build|develop|test|deploy)\b/gi,
      requirement: /\b(must|should|shall|required|mandatory|optional)\b/gi,
      technology: /\b(React|Vue|Angular|Node|Python|Java|JavaScript|TypeScript)\b/gi
    }

    Object.entries(patterns).forEach(([type, pattern]) => {
      const matches = content.match(pattern) || []
      const uniqueMatches = [...new Set(matches.map(m => m.toLowerCase()))]
      
      uniqueMatches.forEach(match => {
        entities.push({
          entity: match,
          type: type as EntityType,
          confidence: 0.6, // Lower confidence for keyword-based
          user_selected: false,
          context: 'Keyword-based extraction',
          category: 'technical'
        })
      })
    })

    return entities.slice(0, config.max_entities)
  }

  /**
   * Generate insights from content and entities
   */
  async generateInsights(content: string, entities: ExtractedEntity[]): Promise<KeyInsight[]> {
    const insights: KeyInsight[] = []

    // Content length insight
    if (content.length > 10000) {
      insights.push({
        insight: 'Large document with comprehensive content',
        importance: 'medium',
        category: 'analysis',
        confidence: 0.9,
        supporting_entities: []
      })
    }

    // Entity diversity insight
    const entityTypes = new Set(entities.map(e => e.type))
    if (entityTypes.size > 5) {
      insights.push({
        insight: 'Document covers multiple domains and concepts',
        importance: 'high',
        category: 'summary',
        confidence: 0.85,
        supporting_entities: entities.slice(0, 3).map(e => e.entity)
      })
    }

    // High confidence entities insight
    const highConfidenceEntities = entities.filter(e => e.confidence > 0.8)
    if (highConfidenceEntities.length > 0) {
      insights.push({
        insight: `Document contains ${highConfidenceEntities.length} clearly identifiable key concepts`,
        importance: 'high',
        category: 'analysis',
        confidence: 0.9,
        supporting_entities: highConfidenceEntities.map(e => e.entity)
      })
    }

    return insights
  }

  /**
   * Save intelligence session to vault storage
   */
  async saveSession(session: DocumentIntelligenceSession): Promise<boolean> {
    try {
      const intelligenceDir = await this.getIntelligenceDirectory(session.document.vault)
      const documentDir = `${intelligenceDir}/documents/${session.document.hash}`
      const sessionDir = `${documentDir}/sessions`
      
      // Ensure directories exist
      await this.ensureDirectoryExists(sessionDir)
      
      // Save session file
      const sessionPath = `${sessionDir}/session_${session.timestamp.replace(/[:.]/g, '-')}.json`
      const result = await window.electronAPI.vault.writeFile(
        sessionPath, 
        JSON.stringify(session, null, 2)
      )

      if (result.success) {
        // Update document profile
        await this.updateDocumentProfile(session)
        
        // Update entity index
        await this.updateEntityIndex(session.intelligence_session.extracted_entities, session.document.hash)
        
        this.logger.info('Intelligence session saved', 'saveSession', {
          sessionId: session.session_id,
          documentHash: session.document.hash
        })
        
        return true
      } else {
        throw new Error(result.error || 'Failed to save session')
      }
    } catch (error) {
      this.logger.error('Failed to save intelligence session', 'saveSession', error as Error)
      return false
    }
  }

  /**
   * Load session history for a document
   */
  async loadSessionHistory(documentHash: string): Promise<DocumentIntelligenceSession[]> {
    try {
      // Check cache first
      if (this.sessionCache.has(documentHash)) {
        return this.sessionCache.get(documentHash)!
      }

      // Load from storage
      const sessions: DocumentIntelligenceSession[] = []
      // Implementation would read from vault storage
      // For now, return empty array
      
      this.sessionCache.set(documentHash, sessions)
      return sessions
    } catch (error) {
      this.logger.error('Failed to load session history', 'loadSessionHistory', error as Error)
      return []
    }
  }

  /**
   * Update entity index with new entities
   */
  async updateEntityIndex(entities: ExtractedEntity[], documentHash: string): Promise<void> {
    // Implementation would update the global entity index
    // This is a placeholder for the actual implementation
    this.logger.info('Entity index updated', 'updateEntityIndex', {
      entitiesCount: entities.length,
      documentHash
    })
  }

  /**
   * Create a new intelligence session
   */
  createSession(
    document: DocumentMetadata,
    analysis: IntelligenceAnalysis,
    userInteractions: UserInteraction[] = []
  ): DocumentIntelligenceSession {
    const session: DocumentIntelligenceSession = {
      session_id: this.generateSessionId(),
      timestamp: new Date().toISOString(),
      document,
      intelligence_session: analysis,
      user_interactions: userInteractions,
      context_signals: {
        user_intent: 'document_analysis',
        document_importance: 'medium',
        workflow_stage: 'analysis',
        related_documents: analysis.related_documents || [],
        access_frequency: 1,
        time_spent_seconds: 0,
        interaction_depth: 'moderate'
      },
      learning_data: {
        entity_patterns: {},
        user_preferences: {
          preferred_tags: [],
          annotation_style: 'concise',
          detail_level: 'medium',
          ai_confidence_threshold: 0.7,
          auto_tag_enabled: true
        },
        workflow_patterns: [],
        content_clusters: []
      }
    }

    this.currentSession = session
    return session
  }

  // Helper methods
  private async generateDocumentHash(content: string, filePath: string): Promise<string> {
    const encoder = new TextEncoder()
    const data = encoder.encode(content + filePath)
    const hashBuffer = await crypto.subtle.digest('SHA-256', data)
    const hashArray = Array.from(new Uint8Array(hashBuffer))
    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('')
  }

  private generateSessionId(): string {
    return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
  }

  private mapEntityType(type: string): EntityType {
    const typeMap: Record<string, EntityType> = {
      'person': 'person',
      'organization': 'organization',
      'location': 'location',
      'technology': 'technology',
      'concept': 'technical_concept',
      'other': 'other'
    }
    return typeMap[type] || 'other'
  }

  private calculateOverallConfidence(entities: ExtractedEntity[], insights: KeyInsight[]): number {
    if (entities.length === 0) return 0
    
    const entityConfidence = entities.reduce((sum, e) => sum + e.confidence, 0) / entities.length
    const insightConfidence = insights.length > 0 
      ? insights.reduce((sum, i) => sum + i.confidence, 0) / insights.length 
      : 0.5
    
    return (entityConfidence * 0.7 + insightConfidence * 0.3)
  }

  private async generateContentSummary(content: string, entities: ExtractedEntity[]): Promise<string> {
    const topEntities = entities.slice(0, 5).map(e => e.entity).join(', ')
    const wordCount = content.split(/\s+/).length
    
    return `Document contains ${wordCount} words with key focus on: ${topEntities}`
  }

  private async findRelatedDocuments(_entities: ExtractedEntity[], _currentPath: string): Promise<string[]> {
    // Placeholder - would implement document similarity based on entities
    return []
  }

  private async getIntelligenceDirectory(vaultPath: string): Promise<string> {
    return `${vaultPath}/.intelligence`
  }

  private async ensureDirectoryExists(path: string): Promise<void> {
    await window.electronAPI.vault.createDirectory(path)
  }

  private async updateDocumentProfile(session: DocumentIntelligenceSession): Promise<void> {
    // Placeholder for document profile update
    this.logger.info('Document profile updated', 'updateDocumentProfile', {
      documentHash: session.document.hash
    })
  }

  // Export intelligence data
  async exportIntelligenceData(documentHash: string): Promise<any> {
    const sessions = await this.loadSessionHistory(documentHash)
    return {
      documentHash,
      sessions,
      exportedAt: new Date().toISOString()
    }
  }

  // Get current session
  getCurrentSession(): DocumentIntelligenceSession | null {
    return this.currentSession
  }

  // Clear current session
  clearCurrentSession(): void {
    this.currentSession = null
  }
}

// Export singleton instance
export const documentIntelligenceService = new DocumentIntelligenceService()
